/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { Test, type TestingModule } from '@nestjs/testing';
import { type ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { ConfigService } from '@nestjs/config';

jest.mock('backoffice-backend-common', () => ({
  guards: {
    AuthenticatedGuard: class MockAuthenticatedGuard {
      async canActivate(context: ExecutionContext): Promise<boolean> {
        await Promise.resolve(); // Para satisfazer a regra require-await
        const request = context.switchToHttp().getRequest();
        const authorization = request.headers?.authorization;

        if (
          !authorization ||
          typeof authorization !== 'string' ||
          !authorization.startsWith('Bearer ')
        ) {
          throw new UnauthorizedException('Token não fornecido');
        }

        const token = authorization.replace('Bearer ', '');

        if (token === 'invalid-token') {
          throw new UnauthorizedException('Token inválido');
        }

        if (token === 'valid-token') {
          request.user = {
            sub: 'user-id',
            username: '<EMAIL>',
            email: '<EMAIL>',
            roles: ['USER'],
          };
          return true;
        }

        throw new UnauthorizedException('Token inválido');
      }
    },
  },
}));

describe('JwtAuthGuard', () => {
  let guard: JwtAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtAuthGuard,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                KEYCLOAK_BASE_URL: 'http://keycloak:8080',
                KEYCLOAK_REALM: 'test',
                KEYCLOAK_CLIENT_ID: 'test-client',
                KEYCLOAK_CLIENT_SECRET: 'test-client-secret',
              };
              return config[key] as string;
            }),
          },
        },
      ],
    }).compile();

    guard = module.get<JwtAuthGuard>(JwtAuthGuard);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should throw UnauthorizedException when no token is provided', async () => {
    const context = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {},
        }),
      }),
    } as ExecutionContext;

    await expect(guard.canActivate(context)).rejects.toThrow(
      UnauthorizedException,
    );
  });

  it('should throw UnauthorizedException when token is invalid', async () => {
    const context = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            authorization: 'Bearer invalid-token',
          },
        }),
      }),
    } as ExecutionContext;

    await expect(guard.canActivate(context)).rejects.toThrow(
      UnauthorizedException,
    );
  });

  it('should return true when token is valid', async () => {
    const context = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            authorization: 'Bearer valid-token',
          },
        }),
      }),
    } as ExecutionContext;

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });
});
