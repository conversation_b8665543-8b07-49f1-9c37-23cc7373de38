import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaSupplierRepository } from '@/infrastructure/repositories/prisma-supplier.repository';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { randomInt } from 'crypto';
import { isAfter, subMinutes } from 'date-fns';
import { IdentityTokenRefreshedEvent } from '../../core/domain/events/identity-token-refreshed.event';
import { IdentityTokenRevokedEvent } from '../../core/domain/events/identity-token-revoked.event';
import { Role } from '../../core/domain/role.enum';
import { PasswordHasher } from '../../core/shared/password-hasher';
import { EmailService } from '../../infrastructure/email/nodemailer-email.service.interface';
import { EventPublisherService } from '../../infrastructure/events/event-publisher.service';
import { KeycloakIdentityProviderService } from '../../infrastructure/keycloak/keycloak-identity-provider.service';
import { KeycloakService } from '../../infrastructure/keycloak/keycloak.service';
import { CustomerRepository } from '../customers/infrastructure/repositories/customer.repository';
import { UsersService } from '../users/users.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { LoginDto, LoginOtpDto, VerifyOtpDto } from './dto/login.dto';
import { LogoutDto } from './dto/logout.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { RegisterDto } from './dto/register.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import { PasswordResetTokenRepository } from './interfaces/password-reset-token.repository.interface';
import { UsersOtpRepository } from './interfaces/users-otp.repository.interface';
import { SupplierAuthService } from './supplier-auth/supplier-auth.service';

interface UserProfile {
  id: string;
  username: string;
  email: string;
  fullName: string;
  roles: string[];
}

interface _JwtDecodedToken {
  sub: string;
  jti: string;
  [key: string]: unknown;
}

// User model representing what we expect from the database
interface UserModel {
  id: string;
  email: string;
  name: string;
  role: string;
  password?: string;
  createdAt?: Date;
  updatedAt?: Date;
  keycloakId?: string;
}

// Define a UserRepository interface for type checking
interface UserRepository {
  findByEmail: (email: string) => Promise<UserModel | null>;
  create: (
    userData: Omit<UserModel, 'id' | 'keycloakId'>,
  ) => Promise<UserModel>;
  findByKeycloakId: (keycloakId: string) => Promise<UserModel>;
  updateUserKeycloakId: (userId: string, keycloakId: string) => Promise<void>;
  findById: (id: string) => Promise<UserModel | null>;
  update: (userData: Partial<UserModel>) => Promise<void>;
  findByToken: (token: string) => Promise<UserModel | null>;
}

function isJwtDecodedToken(obj: unknown): obj is _JwtDecodedToken {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'sub' in obj &&
    typeof (obj as _JwtDecodedToken).sub === 'string'
  );
}

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private supplierAuthService: SupplierAuthService,
    private jwtService: JwtService,
    @Inject('UserRepository')
    private userRepository: UserRepository,
    @Inject('PasswordResetTokenRepository')
    private passwordResetTokenRepository: PasswordResetTokenRepository,
    @Inject('EmailService')
    private emailService: EmailService,
    private keycloakService: KeycloakService,
    private eventPublisher: EventPublisherService,
    private keycloakIdentityProvider: KeycloakIdentityProviderService,
    private readonly customerRepository: CustomerRepository,
    private readonly supplierRepository: PrismaSupplierRepository,
    private readonly employeeRepository: PrismaEmployeeRepository,
    @Inject('UsersOtpRepository')
    private readonly usersOtpRepository: UsersOtpRepository, // Assuming this is defined somewhere
  ) {}

  async validateUser(
    email: string,
    _password: string,
  ): Promise<UserModel | null> {
    // Simplified implementation
    try {
      const user = await this.userRepository.findByEmail(email);
      return user;
    } catch {
      return null;
    }
  }

  async token(loginDto: LoginDto) {
    try {
      const result = await this.keycloakService.token(
        loginDto.username,
        loginDto.password,
      );

      // Extrair o ID do usuário do token
      const decodedToken: unknown = this.jwtService.decode(result.access_token);
      // Verificar se o token decodificado é válido
      if (isJwtDecodedToken(decodedToken)) {
        const userId = decodedToken.sub;
        const event = new IdentityTokenRefreshedEvent(
          userId,
          result.access_token,
          {
            userId,
          },
        );
        await this.eventPublisher.publish(event);
      }

      return {
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
      };
    } catch (error) {
      console.error('Erro ao autenticar no Keycloak:', error);
      throw new UnauthorizedException('Usuário ou senha inválidos');
    }
  }

  async register(registerDto: RegisterDto) {
    try {
      // Primeiro, verificar se o usuário já existe no banco local
      const existingUser = await this.userRepository.findByEmail(
        registerDto.email,
      );
      if (existingUser) {
        throw new BadRequestException({ message: 'Email já está em uso' });
      }

      // Preparar dados para o Keycloak - similar ao teste e2e
      const nameParts = registerDto.name.split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      // Use the KeycloakIdentityProviderService to register the user
      let keycloakUserId: string;
      const userRole = registerDto.role || Role.USER;
      try {
        // Create user object for registration
        const keycloakUser = {
          username: registerDto.email, // Username será sempre igual ao email
          email: registerDto.email,
          firstName,
          lastName,
          password: registerDto.password,
        };

        // Register user in Keycloak
        keycloakUserId =
          await this.keycloakIdentityProvider.registerUser(keycloakUser);

        // Assign user roles
        await this.keycloakIdentityProvider.assignUserRoles(keycloakUserId, [
          userRole,
        ]);
      } catch (keycloakError) {
        console.error('Erro ao registrar no Keycloak:', keycloakError);
        throw new BadRequestException({
          message: `Falha ao registrar usuário: ${
            keycloakError instanceof Error
              ? keycloakError.message
              : String(keycloakError)
          }`,
        });
      }

      // Hash da senha antes de salvar no banco local
      const hashedPassword = await PasswordHasher.hash(registerDto.password);

      // Criar usuário local simplificado
      const newUser = await this.userRepository.create({
        name: registerDto.name,
        email: registerDto.email,
        password: hashedPassword,
        role: userRole,
      });

      if (!newUser) {
        // TODO: Apos update vinda de outra PR provisionar rollback do usuário no Keycloak
        // await this.keycloakIdentityProvider.deleteUser(keycloakUserId);
        throw new InternalServerErrorException({
          message: 'Erro ao criar usuário no banco de dados local',
        });
      }

      // Adicionar id do keycloak como atributo do usuário local
      await this.userRepository.updateUserKeycloakId(
        newUser.id,
        keycloakUserId,
      );

      // Criar payload JWT
      const payload = {
        email: newUser.email,
        sub: keycloakUserId, // Usar o ID do Keycloak como subject
        role: newUser.role,
      };

      return {
        message: 'Usuário registrado com sucesso',
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role,
          keycloakId: keycloakUserId,
        },
        access_token: this.jwtService.sign(payload),
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      if (
        error instanceof InternalServerErrorException &&
        error.message === 'Erro ao criar usuário no banco de dados local'
      ) {
        throw error;
      }
      throw new BadRequestException({
        message:
          error instanceof Error
            ? error?.message
              ? error.message
              : 'Não foi possível registrar o usuário'
            : 'Não foi possível registrar o usuário',
      });
    }
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto) {
    try {
      const result = await this.keycloakService.refreshToken(
        refreshTokenDto.refresh_token,
      );

      // Extrair o ID do usuário do token antigo
      const decodedToken: unknown = this.jwtService.decode(
        refreshTokenDto.refresh_token,
      );
      // Verificar se o token decodificado é válido
      if (isJwtDecodedToken(decodedToken)) {
        const userId = decodedToken.sub;
        const event = new IdentityTokenRefreshedEvent(
          userId,
          refreshTokenDto.refresh_token,
          {
            userId,
          },
        );
        await this.eventPublisher.publish(event);
      }

      return {
        access_token: result.access_token,
      };
    } catch {
      throw new UnauthorizedException(
        'Token de atualização inválido ou expirado',
      );
    }
  }

  async getUserProfile(user: {
    sub: string;
    email: string;
    role: string;
  }): Promise<UserProfileDto> {
    try {
      const { sub: id, email } = user;

      if (!id || !email) {
        throw new UnauthorizedException(
          'Token inválido: informações do usuário incompletas',
        );
      }
      // Buscar usuário no banco para garantir role correto
      const dbUser = await this.userRepository.findByKeycloakId(id);
      const role = dbUser?.role || user.role || 'USER';

      let supplier;
      if (dbUser.role.includes('SUPPLIER')) {
        const supplierByUserId = await this.supplierRepository.findByUserId(
          dbUser.id,
        );
        if (supplierByUserId) {
          supplier = { uuid: supplierByUserId.id };
        }
      }
      let customer;
      if (dbUser.role.includes('CUSTOMER')) {
        const customerByUserId = await this.customerRepository.findByUserId(
          dbUser.id,
        );
        if (customerByUserId) {
          customer = { uuid: customerByUserId.uuid };
        }
      }

      let employee;
      if (dbUser.role.includes('EMPLOYEE')) {
        const employeeByUserId = await this.employeeRepository.findByUserId(
          dbUser.id,
        );
        if (employeeByUserId) {
          employee = { uuid: employeeByUserId.uuid };
        }
      }

      return {
        id: dbUser?.id,
        username: email.split('@')[0],
        email,
        fullName: dbUser?.name || '',
        roles: [role],
        customer: customer || undefined,
        supplier: supplier || undefined,
        employee: employee || undefined,
      };
    } catch (error: unknown) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Token inválido ou expirado', {
        cause: error,
      });
    }
  }

  async getProfile(token: string): Promise<UserProfile> {
    try {
      // Validate token with Keycloak
      const isValid = await this.keycloakService.validateToken(token);
      if (!isValid) {
        throw new UnauthorizedException('Token inválido');
      }

      // Get user info from Keycloak
      const userInfo = await this.keycloakService.getUserInfo(token);

      // Validate required claims
      if (!userInfo.sub || !userInfo.preferred_username || !userInfo.email) {
        throw new UnauthorizedException('Informações do usuário incompletas');
      }

      // Map Keycloak claims to user profile
      return {
        id: userInfo.sub,
        username: userInfo.preferred_username,
        email: userInfo.email,
        fullName: userInfo.name || '',
        roles: userInfo.roles || [],
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException(
        'Não foi possível obter o perfil do usuário',
      );
    }
  }

  async logout(logoutDto: LogoutDto): Promise<void> {
    try {
      // Extrair o ID do usuário do token
      const decodedToken: unknown = this.jwtService.decode(
        logoutDto.refresh_token,
      );

      // Verificar se o token decodificado é válido
      if (isJwtDecodedToken(decodedToken)) {
        const userId = decodedToken.sub;
        const tokenId = decodedToken.jti;

        // Revogar o token no Keycloak
        await this.keycloakService.logout(logoutDto.refresh_token);

        // Publicar evento de revogação
        const event = new IdentityTokenRevokedEvent(userId, tokenId, {
          userId,
        });
        await this.eventPublisher.publish(event);
      } else {
        throw new BadRequestException('Token de atualização inválido');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Não foi possível realizar o logout');
    }
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const { email } = forgotPasswordDto;

    // Check if user exists
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      // Return 204 even if user doesn't exist to prevent email enumeration
      return;
    }

    // Generate secure random token
    const token = Math.floor(100000 + Math.random() * 900000).toString();

    // Delete any existing tokens for this user to prevent token accumulation
    await this.passwordResetTokenRepository.deleteByUserId(user.id);

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1); // Token expires in 1 hour

    // Save token
    await this.passwordResetTokenRepository.create({
      userId: user.id,
      token,
      expiresAt,
      used: false,
    });

    try {
      // Send password reset email
      await this.emailService.sendPasswordResetEmail(email, token);
    } catch (emailError) {
      console.error('Erro ao enviar email de recuperação:', emailError);
      // Don't throw error to prevent information disclosure
      // Token is still created, user can try again or contact support
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, newPassword } = resetPasswordDto;

    // Validate password strength
    if (!this.isValidPassword(newPassword)) {
      throw new BadRequestException(
        'A senha deve ter pelo menos 8 caracteres, incluindo uma letra maiúscula, uma minúscula, um número e um caractere especial',
      );
    }

    // Find the reset token
    const resetToken =
      await this.passwordResetTokenRepository.findByToken(token);
    if (!resetToken) {
      throw new UnauthorizedException('Token inválido ou expirado');
    }

    // Check if token is expired or used
    if (resetToken.used || resetToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Token inválido ou expirado');
    }

    // Get the user
    const user = await this.userRepository.findById(resetToken.userId);
    if (!user) {
      throw new UnauthorizedException('Usuário não encontrado');
    }

    if (!user.keycloakId) {
      throw new InternalServerErrorException(
        'Usuário não possui ID do Keycloak',
      );
    }

    try {
      // Reset password in Keycloak
      await this.keycloakService.resetPassword(user.keycloakId, newPassword);

      // Hash da nova senha antes de salvar no banco local
      const hashedPassword = await PasswordHasher.hash(newPassword);

      // Update password in local database
      await this.userRepository.update({
        id: user.id,
        password: hashedPassword,
      });

      // Mark token as used
      await this.passwordResetTokenRepository.markAsUsed(token);

      console.log(`Senha redefinida com sucesso para usuário ${user.email}`);
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      throw new InternalServerErrorException('Erro ao redefinir senha');
    }
  }

  private isValidPassword(password: string): boolean {
    // At least 8 characters, one uppercase, one lowercase, one number, one special character
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  async loginWithOtp(loginOtpDto: LoginOtpDto) {
    try {
      // Validate user exists
      const user = await this.userRepository.findByEmail(loginOtpDto.email);
      if (!user) {
        throw new UnauthorizedException('Usuário não encontrado');
      }
      // Delete any existing OTP for the user
      await this.usersOtpRepository.deleteOtpByUserId(user.id);

      const code = String(randomInt(0, 1_000_000)).padStart(6, '0');
      const hash = await bcrypt.hash(code, 10);
      const expiresAt = subMinutes(new Date(), -5); // OTP válido por 5 minutos

      // Store OTP securely (e.g., in a database or cache)
      await this.usersOtpRepository.createOtp(user.id, hash, expiresAt);

      try {
        // Send OTP via email
        if (this.emailService.sendSecurityNotificationEmailWithToken) {
          await this.emailService.sendSecurityNotificationEmailWithToken(
            user.email,
            code,
          );
        }
      } catch (emailError) {
        console.error('Erro ao enviar email de OTP:', emailError);
        throw new InternalServerErrorException(
          'Erro ao enviar email de OTP. Tente novamente mais tarde.',
        );
      }

      // Simulate sending OTP via email
      console.log(`OTP enviado para o email ${user.email}: ${code}`);

      return { message: 'OTP enviado para o email do usuário' };
    } catch (error) {
      console.error('Erro ao enviar OTP:', error);
      throw new InternalServerErrorException('Erro ao enviar OTP');
    }
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const user = await this.userRepository.findByEmail(verifyOtpDto.email);
    if (!user) {
      throw new UnauthorizedException('Usuário não encontrado');
    }

    const otps = await this.usersOtpRepository.findOtpByUserId(user.id);
    const otp = otps
      .filter((o) => isAfter(o.expiresAt, new Date()))
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];

    if (!otp) {
      throw new UnauthorizedException(
        'Nenhum OTP válido encontrado para o usuário',
      );
    }

    const match = await bcrypt.compare(verifyOtpDto.code, otp.hash);
    if (!match) {
      throw new UnauthorizedException('Código OTP inválido');
    }

    // If OTP is valid, delete it
    await this.usersOtpRepository.deleteOtp(otp.id);

    return this.token({
      username: verifyOtpDto.email,
      password: verifyOtpDto.password,
    });
  }
}
