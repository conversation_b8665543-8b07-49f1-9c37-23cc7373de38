module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Enforce English language for commits
    'subject-case': [2, 'always', 'lower-case'],
    'type-enum': [
      2,
      'always',
      [
        'feat',     // Nova funcionalidade
        'fix',      // Correção de bug
        'chore',    // Tarefas de manutenção
        'docs',     // Alterações em documentação
        'test',     // Adição ou modificação de testes
        'refactor', // Refatoração de código sem alterar comportamento
        'style',    // Formatação, espaços em branco, etc
        'perf',     // Melhorias de performance
        'ci',       // Mudanças nos arquivos de CI
        'build',    // Mudanças no sistema de build
        'revert'    // Reversão de commit
      ]
    ],
    'type-case': [2, 'always', 'lower-case'],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'header-max-length': [2, 'always', 100],
    'body-leading-blank': [1, 'always'],
    'footer-leading-blank': [1, 'always']
  }
};