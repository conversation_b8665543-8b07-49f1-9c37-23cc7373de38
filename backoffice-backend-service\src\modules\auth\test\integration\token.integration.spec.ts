import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { ConfigModule } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { AuthModule } from '../../auth.module';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { UsersService } from '../../../users/users.service';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { DomainEventsModule } from '../../../../infrastructure/events/domain-events.module';
import { UsersModule } from '../../../users/users.module';
import { Server } from 'http';

interface TokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

interface TokenResponseBody {
  access_token?: string;
  refresh_token?: string;
  expires_in?: number;
  message?: string;
}

// Mock das dependências necessárias
const mockPrismaService = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $executeRawUnsafe: jest.fn(),
  user: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

const mockUsersService = {
  findByEmail: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  findAll: jest.fn(),
};

// Helper to satisfy supertest's expected type
function getSupertestServer(app: INestApplication): Server {
  return app.getHttpServer() as Server;
}

describe('AccessToken (e2e)', () => {
  let app: INestApplication;
  let keycloakService: KeycloakService;
  let eventPublisher: EventPublisherService;
  let jwtService: JwtService;

  beforeEach(async () => {
    try {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            isGlobal: true,
            // Valores padrão para testes
            envFilePath: '.env.test',
            load: [
              () => ({
                KEYCLOAK_BASE_URL: 'http://localhost:8080',
                KEYCLOAK_REALM: 'test-realm',
                KEYCLOAK_CLIENT_ID: 'test-client',
                KEYCLOAK_CLIENT_SECRET: 'test-secret',
                KEYCLOAK_ADMIN_CLIENT_SECRET: 'test-admin-secret',
                KEYCLOAK_MAX_RETRIES: 3,
                KEYCLOAK_INITIAL_RETRY_DELAY_MS: 100,
                KEYCLOAK_ERROR_THRESHOLD_PERCENTAGE: 50,
                KEYCLOAK_RESET_TIMEOUT: 60000,
                KEYCLOAK_ROLLING_COUNT_MAX_ERRORS: 5,
              }),
            ],
          }),
          JwtModule.register({
            secret: 'test-jwt-secret',
            signOptions: { expiresIn: '1h' },
          }),
          EventsTestModule,
          AuthModule,
          UsersModule,
          DomainEventsModule,
        ],
      })
        .overrideProvider(PrismaService)
        .useValue(mockPrismaService)
        .overrideProvider(UsersService)
        .useValue(mockUsersService)
        .overrideProvider(KeycloakService)
        .useValue({
          token: jest
            .fn()
            .mockImplementation(async (username: string, password: string) => {
              await Promise.resolve(); // Para satisfazer a regra require-await
              if (
                username === '<EMAIL>' &&
                password === 'senha123'
              ) {
                return {
                  access_token: 'access-token-valido',
                  refresh_token: 'refresh-token-valido',
                  expires_in: 300,
                  token_type: 'Bearer',
                };
              }
              throw new Error('Usuário ou senha inválidos');
            }),
        })
        .overrideProvider(EventPublisherService)
        .useValue({
          publish: jest.fn().mockResolvedValue(undefined),
        })
        .overrideModule(DomainEventsModule)
        .useModule(EventsTestModule)
        .compile();

      app = moduleFixture.createNestApplication();
      keycloakService = moduleFixture.get<KeycloakService>(KeycloakService);
      eventPublisher = moduleFixture.get<EventPublisherService>(
        EventPublisherService,
      );
      jwtService = moduleFixture.get<JwtService>(JwtService);
      app.setGlobalPrefix('api/v1');
      await app.init();
    } catch (error) {
      console.error('Erro ao configurar o módulo de teste:', error);
      throw error;
    }
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  it('auth/login (POST) - Sucesso com token válido', async () => {
    // Arrange
    const mockAccessToken = 'access-token-valido';
    const tokenFn = jest.spyOn(keycloakService, 'token');
    const _publishSpy = jest.spyOn(eventPublisher, 'publish');
    const decodeSpy = jest.spyOn(jwtService, 'decode');

    tokenFn.mockResolvedValueOnce({
      access_token: mockAccessToken,
      refresh_token: 'refresh-token-valido',
      expires_in: 300,
      token_type: 'Bearer',
    } as TokenResponse);

    decodeSpy.mockReturnValueOnce({
      sub: 'user-123',
      jti: 'token-id',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000),
    });

    // Act
    const response = await request(getSupertestServer(app))
      .post('/api/v1/auth/login')
      .send({
        username: '<EMAIL>',
        password: 'senha123',
      })
      .expect(200);

    const body = response.body as TokenResponseBody;
    expect(body).toHaveProperty('access_token');
    expect(body.access_token).toBe(mockAccessToken);
    expect(tokenFn).toHaveBeenCalledWith('<EMAIL>', 'senha123');
    expect(decodeSpy).toHaveBeenCalledWith(mockAccessToken);
    expect(_publishSpy).toHaveBeenCalled();
  });

  it('/v1/auth/login (POST) - Falha com credenciais inválidas', async () => {
    const tokenFn = jest
      .spyOn(keycloakService, 'token')
      .mockRejectedValue(new Error('Usuário ou senha inválidos'));

    const response = await request(getSupertestServer(app))
      .post('/api/v1/auth/login')
      .send({
        username: '<EMAIL>',
        password: 'senha-incorreta',
      });

    const body = response.body as TokenResponseBody;
    expect(response.status).toBe(401);
    expect(body).toHaveProperty('message');
    expect(body.message).toBe('Usuário ou senha inválidos');
    expect(tokenFn).toHaveBeenCalledWith(
      '<EMAIL>',
      'senha-incorreta',
    );
  });
});
